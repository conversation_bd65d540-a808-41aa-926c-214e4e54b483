<template>
  <div class="app-container home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1>欢迎使用医疗大数据人工智能训练平台</h1>
          <p>智能化医疗数据分析与AI模型训练的专业平台</p>
        </div>
        <div class="banner-image">
          <el-icon size="120" color="#409EFF">
            <DataAnalysis />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card">
          <div class="stat-icon projects">
            <el-icon size="40">
              <FolderOpened />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.projects }}</div>
            <div class="stat-label">训练项目</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card">
          <div class="stat-icon models">
            <el-icon size="40">
              <Cpu />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.models }}</div>
            <div class="stat-label">AI模型</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card">
          <div class="stat-icon datasets">
            <el-icon size="40">
              <DataBoard />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.datasets }}</div>
            <div class="stat-label">数据集</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <div class="stat-card">
          <div class="stat-icon users">
            <el-icon size="40">
              <User />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.users }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h2>快捷操作</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-card class="action-card" shadow="hover" @click="navigateTo('/trainplatform/project')">
            <div class="action-content">
              <el-icon size="48" color="#67C23A">
                <Plus />
              </el-icon>
              <h3>创建新项目</h3>
              <p>开始一个新的AI训练项目</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-card class="action-card" shadow="hover" @click="navigateTo('/trainplatform/files')">
            <div class="action-content">
              <el-icon size="48" color="#E6A23C">
                <Upload />
              </el-icon>
              <h3>上传数据</h3>
              <p>上传训练数据集</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-card class="action-card" shadow="hover" @click="navigateTo('/trainplatform/detection')">
            <div class="action-content">
              <el-icon size="48" color="#F56C6C">
                <View />
              </el-icon>
              <h3>模型检测</h3>
              <p>查看模型训练结果</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
          <el-card class="action-card" shadow="hover" @click="navigateTo('/system/user')">
            <div class="action-content">
              <el-icon size="48" color="#909399">
                <Setting />
              </el-icon>
              <h3>系统管理</h3>
              <p>用户和权限管理</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h2>最近活动</h2>
      <el-card shadow="never">
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :color="activity.color"
          >
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup name="Index">
import {
  DataAnalysis,
  FolderOpened,
  Cpu,
  DataBoard,
  User,
  Plus,
  Upload,
  View,
  Setting
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 统计数据
const stats = ref({
  projects: 156,
  models: 89,
  datasets: 234,
  users: 67
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '新建AI训练项目',
    description: '用户张三创建了"肺部CT影像识别"项目',
    timestamp: '2024-01-15 14:30',
    color: '#67C23A'
  },
  {
    id: 2,
    title: '模型训练完成',
    description: '"心电图异常检测"模型训练已完成，准确率达到95.2%',
    timestamp: '2024-01-15 12:15',
    color: '#409EFF'
  },
  {
    id: 3,
    title: '数据集上传',
    description: '新增"糖尿病患者数据集"，包含10,000条记录',
    timestamp: '2024-01-15 10:45',
    color: '#E6A23C'
  },
  {
    id: 4,
    title: '系统更新',
    description: '平台升级至v3.8.9版本，新增多项功能',
    timestamp: '2024-01-14 16:20',
    color: '#909399'
  }
])

// 导航函数
const navigateTo = (path) => {
  router.push(path)
}

// 动态更新统计数据
onMounted(() => {
  // 这里可以调用API获取真实数据
  // 暂时使用模拟数据展示动画效果
  animateNumbers()
})

const animateNumbers = () => {
  const targets = {
    projects: 156,
    models: 89,
    datasets: 234,
    users: 67
  }

  Object.keys(targets).forEach(key => {
    let current = 0
    const target = targets[key]
    const increment = target / 50

    const timer = setInterval(() => {
      current += increment
      if (current >= target) {
        stats.value[key] = target
        clearInterval(timer)
      } else {
        stats.value[key] = Math.floor(current)
      }
    }, 30)
  })
}
</script>

<style scoped lang="scss">
.home {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
  padding: 20px;
}

// 欢迎横幅样式
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 30px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .welcome-text {
      flex: 1;

      h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 16px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
      }
    }

    .banner-image {
      flex-shrink: 0;
      margin-left: 40px;
      opacity: 0.8;
    }
  }
}

// 统计卡片样式
.stats-row {
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 20px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;

    &.projects {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    &.models {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }

    &.datasets {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
    }

    &.users {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      color: white;
    }
  }

  .stat-content {
    flex: 1;

    .stat-number {
      font-size: 2.2rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #7f8c8d;
      font-weight: 500;
    }
  }
}

// 快捷操作样式
.quick-actions {
  margin-bottom: 40px;

  h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 24px;
    position: relative;
    padding-left: 20px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
    }
  }
}

.action-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  :deep(.el-card__body) {
    padding: 30px 20px;
  }

  .action-content {
    text-align: center;

    h3 {
      color: #2c3e50;
      font-size: 1.2rem;
      font-weight: 600;
      margin: 16px 0 8px 0;
    }

    p {
      color: #7f8c8d;
      font-size: 0.9rem;
      margin: 0;
      line-height: 1.4;
    }
  }
}

// 最近活动样式
.recent-activity {
  h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 24px;
    position: relative;
    padding-left: 20px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
    }
  }

  :deep(.el-card) {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  :deep(.el-card__body) {
    padding: 30px;
  }

  .activity-content {
    h4 {
      color: #2c3e50;
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    p {
      color: #7f8c8d;
      font-size: 0.9rem;
      margin: 0;
      line-height: 1.5;
    }
  }

  :deep(.el-timeline-item__timestamp) {
    color: #909399;
    font-size: 0.8rem;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home {
    padding: 15px;
  }

  .welcome-banner {
    padding: 30px 20px;

    .banner-content {
      flex-direction: column;
      text-align: center;

      .welcome-text {
        h1 {
          font-size: 2rem;
        }

        p {
          font-size: 1rem;
        }
      }

      .banner-image {
        margin: 20px 0 0 0;
      }
    }
  }

  .stat-card {
    padding: 20px;

    .stat-icon {
      width: 60px;
      height: 60px;
      margin-right: 15px;
    }

    .stat-content {
      .stat-number {
        font-size: 1.8rem;
      }
    }
  }

  .action-card {
    :deep(.el-card__body) {
      padding: 25px 15px;
    }
  }

  .recent-activity {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}

@media (max-width: 480px) {
  .welcome-banner {
    .banner-content {
      .welcome-text {
        h1 {
          font-size: 1.6rem;
        }

        p {
          font-size: 0.9rem;
        }
      }
    }
  }

  .stat-card {
    flex-direction: column;
    text-align: center;

    .stat-icon {
      margin: 0 0 15px 0;
    }
  }
}
</style>
